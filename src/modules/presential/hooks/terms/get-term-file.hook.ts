"use client";
import { useQuery } from "@tanstack/react-query";

import { getTermFile } from "../../services/requests/terms/get-file";

export const useGetTermFileQuery = (idTermo: string | number, enabled = true) => {
	return useQuery({
		queryKey: ["terms", "file", "get-file", idTermo],
		queryFn: async () => {
			const res = await getTermFile(idTermo);
			if (!res.success) throw new Error(res.data.message);

			// Converter string Base64 de volta para ArrayBuffer
			const base64String = res.data;
			const binaryString = atob(base64String);
			const uint8Array = new Uint8Array(binaryString.length);

			for (let i = 0; i < binaryString.length; i++) {
				uint8Array[i] = binaryString.charCodeAt(i);
			}

			return uint8Array.buffer;
		},
		enabled: enabled && !!idTermo,
	});
};
