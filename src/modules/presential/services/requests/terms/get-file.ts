"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";

export const getTermFile = async (idTermo: string | number): Promise<ApiResponseReturn<string>> => {
	const response = await createRequestAdmin<ArrayBuffer>({
		method: "GET",
		path: TERM_ENDPOINTS.GET_FILE(idTermo),
		responseType: "arraybuffer",
	});

	if (!response.success) {
		return response;
	}

	const buffer = response.data;
	const uint8Array = new Uint8Array(buffer);
	const base64 = Buffer.from(uint8Array).toString("base64");

	return {
		success: true,
		data: base64,
		status: response.status,
	};
};
