import { getDocument, GlobalWorkerOptions, PDFDocumentLoadingTask, PDFDocumentProxy } from "pdfjs-dist";

export interface IPdfService {
	getPdfDocumentProxy(options: { id: string; buffer: ArrayBuffer }): Promise<PDFDocumentProxy | undefined>;
	clearCache(): void;
}

export interface IPdfLoaderConfig {
	workerSrc: string;
	cMapUrl: string;
	cMapPacked: boolean;
}

class PdfCache {
	private cache = new Map<string, PDFDocumentProxy>();

	get(id: string): PDFDocumentProxy | undefined {
		return this.cache.get(id);
	}

	set(id: string, pdf: PDFDocumentProxy): void {
		this.cache.set(id, pdf);
	}

	has(id: string): boolean {
		return this.cache.has(id);
	}

	clear(): void {
		this.cache.clear();
	}
}

export class PdfService implements IPdfService {
	private cache = new PdfCache();
	private loaderConfig: IPdfLoaderConfig;

	constructor(loaderConfig?: Partial<IPdfLoaderConfig>) {
		this.loaderConfig = {
			workerSrc: "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.10.38/pdf.worker.mjs",
			cMapUrl: "cmaps/",
			cMapPacked: true,
			...loaderConfig,
		};

		GlobalWorkerOptions.workerSrc = this.loaderConfig.workerSrc;
	}

	public async getPdfDocumentProxy({ id, buffer }: { id: string; buffer: ArrayBuffer }): Promise<PDFDocumentProxy | undefined> {
		if (!buffer || buffer.byteLength === 0) {
			console.error("Buffer de PDF vazio ou inválido");
			return undefined;
		}

		if (this.cache.has(id)) {
			return this.cache.get(id)!;
		}

		try {
			const clonedBuffer = buffer.slice(0);
			console.log("está aqui", clonedBuffer);
			const loadingTask: PDFDocumentLoadingTask = getDocument({
				data: clonedBuffer,
				disableFontFace: false,
				cMapUrl: this.loaderConfig.cMapUrl,
				cMapPacked: this.loaderConfig.cMapPacked,
			});

			console.log("L", loadingTask);

			const pdf: PDFDocumentProxy = await loadingTask.promise;

			console.log(pdf);

			this.cache.set(id, pdf);

			return pdf;
		} catch (error) {
			console.log(error);
			console.error(`Erro ao renderizar o PDF para o id ${id}:`, error);
			throw new Error(`Falha ao carregar o documento PDF para o id: ${id}`);
		}
	}

	public clearCache(): void {
		this.cache.clear();
	}
}
